import {
  Component,
  ElementRef,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  OnIni<PERSON>,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { takeUntil } from 'rxjs';

import {
  ALLOWED_EXCEL_FILE_TYPES,
  DATA_MIGRATE_ADV_EXCEL_TEMPLATE,
  DATA_MIGRATE_EXCEL_TEMPLATE,
  ExcelBulkDataMigration,
  ExcelBulkLeadMigration,
  LEAD_MIGRATE_ADV_EXCEL_TEMPLATE,
  LEAD_MIGRATE_EXCEL_TEMPLATE,
  LEAD_MIGRATE_REFFERAL_ADV_EXCEL_TEMPLATE,
  LEAD_MIGRATE_REFFERAL_EXCEL_TEMPLATE
} from 'src/app/app.constants';
import {
  BulkuploadDuplicateOptions,
  BulkuploadDuplicateSubOptions,
} from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  getAppName,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { ExcelUploadedStatusComponent } from 'src/app/features/leads/excel-uploaded-status/excel-uploaded-status.component';
import {
  DataExcelUpload,
  DataMigrateUploadMappedColumns,
  FetchDataMigrateExcelUploadedList,
} from 'src/app/reducers/data/data-management.actions';
import {
  getDataColumnHeadings,
  getDuplicateData,
} from 'src/app/reducers/data/data-management.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  FetchMigrateExcelUploadedList,
  LeadExcelUpload,
  UploadMigrateMappedColumns,
} from 'src/app/reducers/lead/lead.actions';
import {
  getColumnHeadings,
  getDuplicateLeads,
} from 'src/app/reducers/lead/lead.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchAdminsAndReportees,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getAdminsAndReportees,
  getUserBasicDetails,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';

@Component({
  selector: 'migration-bulk-upload',
  templateUrl: './migration-bulk-upload.component.html',
})
export class MigrationBulkUploadComponent implements OnInit, OnDestroy {
  tick: AnimationOptions = { path: 'assets/animations/tick.json' };
  isTemplateDownloaded: boolean = false;
  isFileDataUpdated: boolean = false;
  isFileUploadTriggered: boolean = false;
  isFileTypeSupported: boolean = true;
  currentStep: number = 1;
  isValidModal: boolean = false;
  autoMapEnabled: boolean = false;
  selectedFile: File;
  @ViewChild('validModal') validModal: any;
  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;
  allowedFileTypes: string = ALLOWED_EXCEL_FILE_TYPES;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  duplicateLeads: any = {};
  leadMappingForm: FormGroup;
  dataMappingForm: FormGroup;
  formKeys: Array<string> = [];
  s3BucketKey: string;
  res: boolean = false;
  duplicateUsers: any[] = [];
  allActiveUsers: any[] = [];
  activeUsers: any[];
  selectedUserId: string[] = [];
  notUploadedLeadsExcelPath: string;
  count: any;
  userData: any;
  excelSheets: any = {};
  sheetNames: Array<string> = [];
  selectedSheet: string;
  selectedName: string;
  selectedNumber: string;
  canAssignToAny: boolean = false;
  selectedOption: BulkuploadDuplicateOptions = BulkuploadDuplicateOptions.None;
  selectedSubOption: BulkuploadDuplicateSubOptions =
    BulkuploadDuplicateSubOptions.All;
  leadBulkFields = ExcelBulkLeadMigration;
  dataBulkFields = ExcelBulkDataMigration;
  moduleName =
    window.location.pathname == '/global-config/leads-migration'
      ? 'leads'
      : 'data';
  excelTemplatePath: string;
  options = [
    { label: `New ${this.moduleName}`, value: BulkuploadDuplicateOptions.None },
    {
      label: `Create Duplicate ${this.moduleName}`,
      value: BulkuploadDuplicateOptions.CreateDuplicateLead,
    },
    {
      label: `Override Existing ${this.moduleName}`,
      value: BulkuploadDuplicateOptions.OverideExisitingLeadInformation,
    },
    {
      label: 'Update Missing Information',
      value: BulkuploadDuplicateOptions.UpdateMissingInformation,
    },
  ];

  subOptions = [
    { label: 'All', value: BulkuploadDuplicateSubOptions.All },
    { label: 'Parent only', value: BulkuploadDuplicateSubOptions.ParentOnly },
    {
      label: 'Latest Child Lead',
      value: BulkuploadDuplicateSubOptions.LatestChildLead,
    },
  ];
  globalSettingsDetails: any;

  // get filteredOptions() {
  //   return this.moduleName === 'data'
  //     ? this.options.filter(
  //       (option) =>
  //         option?.value !== BulkuploadDuplicateOptions.CreateDuplicateLead
  //     )
  //     : this.options;
  // }
  getAppName = getAppName;

  constructor(
    public modalRef: BsModalRef,
    public modalService: BsModalService,
    private _store: Store<AppState>,
    private router: Router,
    private fb: FormBuilder,
    private headerTitle: HeaderTitleService,
    public metaTitle: Title
  ) {
    if (this.moduleName == 'leads') {
      this.metaTitle.setTitle('CRM | Import Leads');
      this.headerTitle.setLangTitle('SIDEBAR.import-leads');
    } else {
      this.metaTitle.setTitle('CRM | Import Data');
      this.headerTitle.setLangTitle('SIDEBAR.import-data');
    }
    this.leadMappingForm = this.fb.group({
      CreatedDate: [null],
      Name: [null, Validators.required],
      ContactNo: [null, Validators.required],
      CountryCode: [null],
      AlternateContactNo: [null],
      AlternativeNoCountryCode: [null],
      Email: [null],
      Source: [null],
      SubSource: [null],
      AssignToUser: [null],
      BaseStatus: [null],
      SubStatus: [null],
      ScheduledDate: [null],
      SiteVisitDoneCount: [null],
      SiteVisitNotDoneCount: [null],
      MeetingDoneCount: [null],
      MeetingNotDoneCount: [null],
      EnquiredFor: [null],
      BasePropertyType: [null],
      SubPropertyType: [null],
      NoOfBHK: [null],
      BHKType: [null],
      Beds: [null],
      Baths: [null],
      FurnishStatus: [null],
      PreferredFloor: [null],
      OfferingType: [null],
      Currency: [null],
      UpperBudget: [null],
      LowerBudget: [null],
      Location: [null],
      SubCommunity: [null],
      Community: [null],
      TowerName: [null],
      City: [null],
      State: [null],
      Country: [null],
      Property: [null],
      Project: [null],
      AgencyName: [null],
      ChannelPartnerName: [null],
      ReferralName: [null],
      ReferralContactNo: [null],
      ReferralEmail: [null],
      Notes: [null],
      Tag: [null],
      CampaignName: [null],
      CarpetArea: [null],
      CarpetAreaUnit: [null],
      BuiltUpArea: [null],
      BuiltUpAreaUnit: [null],
      SaleableArea: [null],
      SaleableAreaUnit: [null],
      PropertyArea: [null],
      PropertyAreaUnit: [null],
      NetArea: [null],
      NetAreaUnit: [null],
      UnitName: [null],
      Nationality: [null],
      ClusterName: [null],
      Purpose: [null],
      CompanyName: [null],
      PostalCode: [null],
      Designation: [null],
      ChannelPartnerExecutiveName: [null],
      ChannelPartnerContactNo: [null],
      SourcingManager: [null],
      ClosingManager: [null],
      PossessionDate: [null],
      CustomerCity: [null],
      CustomerState: [null],
      CustomerLocation: [null],
      CustomerCommunity: [null],
      CustomerSubCommunity: [null],
      CustomerTowerName: [null],
      CustomerCountry: [null],
      CustomerPostalCode: [null],
      Profession: [null],
      PossesionType: [null],
    });


    this.dataMappingForm = this.fb.group({
      CreatedDate: [null],
      Name: [null, Validators.required],
      ContactNo: [null, Validators.required],
      CountryCode: [null],
      AlternateContactNo: [null],
      AlternativeNoCountryCode: [null],
      Email: [null],
      Source: [null],
      SubSource: [null],
      AssignToUser: [null],
      BaseStatus: [null],
      ScheduledDate: [null],
      EnquiryTypes: [null],
      BasePropertyType: [null],
      SubPropertyType: [null],
      BHKs: [null],
      BHKTypes: [null],
      Beds: [null],
      Baths: [null],
      FurnishStatus: [null],
      PreferredFloor: [null],
      OfferingType: [null],
      Currency: [null],
      UpperBudget: [null],
      LowerBudget: [null],
      Location: [null],
      SubCommunity: [null],
      Community: [null],
      TowerName: [null],
      City: [null],
      State: [null],
      Country: [null],
      Property: [null],
      Project: [null],
      AgencyName: [null],
      ChannelPartnerName: [null],
      ReferralName: [null],
      ReferralContactNo: [null],
      ReferralEmail: [null],
      Notes: [null],
      CampaignName: [null],
      CarpetArea: [null],
      CarpetAreaUnit: [null],
      BuiltUpArea: [null],
      BuiltUpAreaUnit: [null],
      SaleableArea: [null],
      SaleableAreaUnit: [null],
      PropertyArea: [null],
      PropertyAreaUnit: [null],
      NetArea: [null],
      NetAreaUnit: [null],
      UnitName: [null],
      Nationality: [null],
      ClusterName: [null],
      Purpose: [null],
      CompanyName: [null],
      PostalCode: [null],
      Designation: [null],
      ChannelPartnerExecutiveName: [null],
      ChannelPartnerContactNo: [null],
      SourcingManager: [null],
      ClosingManager: [null],
      PossessionDate: [null],
      CustomerCity: [null],
      CustomerState: [null],
      CustomerLocation: [null],
      CustomerCommunity: [null],
      CustomerSubCommunity: [null],
      CustomerTowerName: [null],
      CustomerCountry: [null],
      CustomerPostalCode: [null],
      Profession: [null],
      PossesionType: [null],
    });
  }

  ngOnInit() {
    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Users.AssignToAny')) {
          this.canAssignToAny = true;
          this._store.dispatch(new FetchUsersListForReassignment());
        } else {
          this._store.dispatch(new FetchAdminsAndReportees());
        }
      });
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsDetails = data;
        if (this.moduleName === 'leads') {
          this.excelTemplatePath =
            this.globalSettingsDetails?.shouldRenameSiteVisitColumn
              ? (this.globalSettingsDetails?.isCustomLeadFormEnabled
                ? LEAD_MIGRATE_REFFERAL_ADV_EXCEL_TEMPLATE
                : LEAD_MIGRATE_REFFERAL_EXCEL_TEMPLATE)
              : (this.globalSettingsDetails?.isCustomLeadFormEnabled
                ? LEAD_MIGRATE_ADV_EXCEL_TEMPLATE
                : LEAD_MIGRATE_EXCEL_TEMPLATE);

                if(this.globalSettingsDetails?.shouldRenameSiteVisitColumn){
                  this.leadBulkFields = this.leadBulkFields.map((item) => {
                    if(item.displayName === 'Site Visit Done (Count)'){
                      item.displayName = 'Referral Taken (Count)';
                    }
                    if(item.displayName === 'Site Visit Not Done (Count)'){
                      item.displayName = 'Referral Not Taken (Count)';
                    }
                    return item;
                  })
                  
                }
        } else if (this.moduleName === 'data') {
          this.excelTemplatePath = this.globalSettingsDetails
            ?.isCustomLeadFormEnabled
            ? DATA_MIGRATE_ADV_EXCEL_TEMPLATE
            : DATA_MIGRATE_EXCEL_TEMPLATE;
        }
      });
    this._store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.activeUsers = data?.filter((user: any) => user.isActive);
        this.activeUsers = this.activeUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.activeUsers = assignToSort(this.activeUsers, '');
      });

    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allActiveUsers = data?.filter((user: any) => user.isActive);
        this.allActiveUsers = this.allActiveUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.allActiveUsers = assignToSort(this.allActiveUsers, '');
      });
  }

  checkFileFormat(fileName: string) {
    const allowedFiles = ['xls', 'xlx', 'csv', 'xlsx'];
    const regex = /(?:\.([^.]+))?$/;
    const extension = regex.exec(fileName);
    this.isFileTypeSupported = allowedFiles.includes(extension[1]);
    return this.isFileTypeSupported;
  }

  onFileSelection(file: File) {
    this.selectedFile = file;
    this.currentStep =
      this.currentStep < 2 ? this.currentStep + 1 : this.currentStep;
  }

  getBulkFields(fields: any[]) {
    if (this.globalSettingsDetails?.isCustomLeadFormEnabled) {
      return fields.filter(
        (field) =>
          field.displayName !== 'BHK' && field.displayName !== 'BHK Type'
      );
    } else {
      return fields.filter(
        (field) =>
          ![
            'Enquired Sub-Community',
            'Enquired Community',
            'Enquired Tower Name',
            'BR',
            'Beds',
            'Baths',
            'Furnish Status',
            'Preferred Floor',
            'Offering Type',
            'Property Area',
            'Property Area Unit',
            'Net Area',
            'Net Area Unit',
            'Unit Number/Name',
            'Cluster Name',
            'Nationality',
            'Customer Community',
            'Customer SubCommunity',
            'Customer TowerName'
          ].includes(field.displayName)
      );
    }
  }

  onAutoMapChange() {
    const isLeadsModule = this.moduleName === 'leads';
    const mappingColumns = isLeadsModule
      ? ExcelBulkLeadMigration
      : ExcelBulkDataMigration;
    const form = isLeadsModule ? this.leadMappingForm : this.dataMappingForm;
    const nameControl = 'Name';
    const phoneControl = 'ContactNo';
    const nameKey = 'Name';
    const phoneKey = 'Primary Number';

    const mappingNameForName = this.formKeys?.includes(nameKey) ? nameKey : null;
    const mappingNameForPhone = this.formKeys?.includes(phoneKey) ? phoneKey : null;

    mappingColumns.forEach(({ displayName, mappingControlName }) => {
      if (displayName && form.controls[mappingControlName] && this.formKeys?.includes(displayName)) {
        form.patchValue({
          [mappingControlName]: this.autoMapEnabled ? displayName : null,
        });
      }
    });

    if (mappingNameForName) {
      form.patchValue({
        [nameControl]: this.autoMapEnabled ? mappingNameForName : null,
        [phoneControl]: this.autoMapEnabled ? mappingNameForPhone : null,
      });
    }
  }


  uploadFile() {
    if (!this.isFileTypeSupported) {
      return;
    }
    if (this.moduleName === 'leads') {
      this._store.dispatch(new LeadExcelUpload(this.selectedFile));
      this._store.select(getColumnHeadings).subscribe((data: any) => {
        this.excelSheets = data?.multiSheetColumnNames;
        if (this.excelSheets) this.sheetNames = Object.keys(this.excelSheets);
        this.selectedSheet = this.sheetNames[0];
        this.formKeys = data?.columnNames;
        this.selectedName = this.formKeys?.find((key) => key === 'Name');
        this.selectedNumber = this.formKeys?.find(
          (key) => key === 'Primary Number'
        );
        this.s3BucketKey = data?.s3BucketKey;
        if (this.formKeys?.length) {
          this.currentStep = 3;
        }
        this.resetForms();
      });
    } else {
      this._store.dispatch(new DataExcelUpload(this.selectedFile));
      this._store.select(getDataColumnHeadings).subscribe((data: any) => {
        this.excelSheets = data?.multiSheetColumnNames;
        if (this.excelSheets) this.sheetNames = Object.keys(this.excelSheets);
        this.selectedSheet = this.sheetNames[0];
        this.formKeys = data?.columnNames;
        this.selectedName = this.formKeys?.find((key) => key === 'Name');
        this.selectedNumber = this.formKeys?.find(
          (key) => key === 'Primary Number'
        );
        this.s3BucketKey = data?.s3BucketKey;
        if (this.formKeys?.length) {
          this.currentStep = 3;
        }
        this.resetForms();
      });
    }
  }

  replaceFile() {
    this.fileInput.nativeElement.click();
  }

  isValidForm() {
    const isLeadsModule = this.moduleName === 'leads';
    const formToValidate = isLeadsModule
      ? this.leadMappingForm
      : this.dataMappingForm;
    if (!this.selectedSheet || !formToValidate.valid) {
      validateAllFormFields(formToValidate);
      return;
    } else {
      this.isValidModal = true;
      this.modalRef = this.modalService.show(this.validModal, {
        class: 'modal-350 modal-dialog-centered ip-modal-unset',
        keyboard: false,
      });
    }
  }

  selectOption(value: any): void {
    this.selectedOption = value;
  }

  confirmSheet() {
    this.currentStep = 4;
    this.modalRef.hide();
  }

  sendLeadMappingDetails(trackerInfoModal: TemplateRef<any>) {
    if (
      !this.selectedSheet ||
      !this.leadMappingForm.controls['Name'].value ||
      !this.leadMappingForm.controls['ContactNo'].value
    ) {
      return;
    }
    const payload: any = {
      s3BucketKey: this.s3BucketKey,
      fileName: this.selectedFile?.name,
      mappedColumnsData: {},
      migrationType: this.selectedOption
        ? this.selectedOption
        : BulkuploadDuplicateOptions.None,
      migrationSubType: this.selectedSubOption
        ? this.selectedSubOption
        : BulkuploadDuplicateSubOptions.All,
      timeZoneId:
        this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
      baseUTcOffset:
        this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
    };

    ExcelBulkLeadMigration.map((item: any) => {
      if (this.leadMappingForm.value[item.mappingControlName]) {
        payload.mappedColumnsData[item.mappingControlName] =
          this.leadMappingForm.value[item.mappingControlName];
      }
    });
    payload.sheetName = this.selectedSheet || this.sheetNames[0];
    this._store.dispatch(new UploadMigrateMappedColumns(payload));
    this._store.dispatch(new FetchMigrateExcelUploadedList(1, 10));
    this._store
      .select(getDuplicateLeads)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (data == null) {
          this.navigateToHome();
        } else {
          this.count = data;
          this.notUploadedLeadsExcelPath = data?.excelUrl;
          // this.currentStep = 5;
          if (this.modalService.getModalsCount() === 0) {
            this.modalRef = this.modalService.show(
              trackerInfoModal,
              Object.assign(
                {},
                {
                  class: 'modal-400 top-modal ph-modal-unset',
                  ignoreBackdropClick: true,
                  keyboard: false,
                }
              )
            );
          }
        }
      });
  }

  sendDataMappingDetails(trackerInfoModal: TemplateRef<any>) {
    if (
      !this.selectedSheet ||
      !this.dataMappingForm?.controls['Name'].value ||
      !this.dataMappingForm?.controls['ContactNo'].value
    ) {
      return;
    }
    const payload: any = {
      s3BucketKey: this.s3BucketKey,
      fileName: this.selectedFile?.name,
      mappedColumnsData: {},
      migrationType: this.selectedOption
        ? this.selectedOption
        : BulkuploadDuplicateOptions.None,
      timeZoneId:
        this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
      baseUTcOffset:
        this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
    };

    ExcelBulkDataMigration.map((item: any) => {
      if (this.dataMappingForm.value[item.mappingControlName]) {
        payload.mappedColumnsData[item.mappingControlName] =
          this.dataMappingForm.value[item.mappingControlName];
      }
    });
    payload.sheetName = this.selectedSheet || this.sheetNames[0];

    this._store.dispatch(new DataMigrateUploadMappedColumns(payload));
    this._store.dispatch(new FetchDataMigrateExcelUploadedList(1, 10));
    this._store
      .select(getDuplicateData)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (data == null) {
          this.navigateToHome();
        } else {
          this.count = data;
          this.notUploadedLeadsExcelPath = data?.excelUrl;
          // this.currentStep = 5;
          if (this.modalService.getModalsCount() === 0) {
            this.modalRef = this.modalService.show(
              trackerInfoModal,
              Object.assign(
                {},
                {
                  class: 'modal-400 top-modal ph-modal-unset',
                  ignoreBackdropClick: true,
                  keyboard: false,
                }
              )
            );
          }
        }
      });
  }

  openBulkUploadedStatusModal() {
    this.navigateToHome();
    if (this.modalRef) {
      this.modalRef.hide();
    }
    let initialState: any = {
      fieldType: location?.href.includes('/global-config/leads-migration')
        ? 'Lead Migration'
        : 'Data Migration',
    };
    this.modalRef = this.modalService.show(ExcelUploadedStatusComponent, {
      class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
      initialState,
    });
  }

  onSheetSelection() {
    this.formKeys = this.excelSheets?.[this.selectedSheet];
  }

  navigateToHome() {
    this.router.navigate(['global-config/migration']);
  }

  resetForms() {
    if (this.leadMappingForm) {
      this.leadMappingForm.reset();
    }
    if (this.dataMappingForm) {
      this.dataMappingForm.reset();
    }
    this.autoMapEnabled = false;
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
