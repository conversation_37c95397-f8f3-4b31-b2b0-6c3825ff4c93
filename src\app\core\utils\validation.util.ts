import { AbstractControl, FormControl } from '@angular/forms';
import { trimString } from './trim.util';
import * as EmailValidator from 'email-validator';

const arrDiff = (a: any[], b: any[]) => {
  return a.filter((x) => !b.includes(x));
};

export class ValidationUtil {
  static fieldMatcher(field: string, errorKey: string) {
    return function (control: FormControl): { [s: string]: boolean } {
      if (!control.parent) return null;
      return control.parent.get(field).value === control.value
        ? null
        : { [errorKey]: true };
    };
  }

  static oneLowerCaseValidator(errorKey: any) {
    return function (control: FormControl) {
      if (!control.value) return null;
      return /^.*[a-z]/.test(control.value) ? null : { [errorKey]: true };
    };
  }

  static oneUpperCaseValidator(errorKey: string) {
    return function (control: FormControl) {
      if (!control.value) return null;
      return /^.*[A-Z]/.test(control.value) ? null : { [errorKey]: true };
    };
  }

  static oneNonAlphaValidator(errorKey: string) {
    return function (control: FormControl) {
      if (!control.value) return null;
      return /^.*[^A-Za-z]/.test(control.value) ? null : { [errorKey]: true };
    };
  }

  static onlyNumbersValidator(errorKey: string) {
    return function (control: AbstractControl) {
      if (!control.value) return null;
      return /^\d+$/.test(control.value) ? null : { [errorKey]: true };
    };
  }

  static cannotBeBlank(control: FormControl) {
    if (control.value) {
      return String(trimSpaces(control.value)).length > 0
        ? null
        : { blank: true };
    }
    return null;
  }

  static phoneNumberValidator(control: FormControl) {
    if (!control.value) return null;
    return /^\d{10}$/.test(trimSpaces(control.value))
      ? null
      : { invalidMobileNumber: true };
  }

  static OTPValidator(control: FormControl) {
    if (!control.value) return null;
    return /^\d{4}$/.test(trimSpaces(control.value))
      ? null
      : { invalidOTPNumber: true };
  }

  static onlyDecimalValidator() {
    return function (control: FormControl) {
      if (!control.value) return null;
      return /^[+-]?([0-9]*[.])?[0-9]+$/.test(control.value)
        ? null
        : { decimal: true };
    };
  }

  static minFloatValue(val: number) {
    return function (control: FormControl) {
      if (!control.value) return null;
      let s = /^[+-]?([0-9]*[.])?[0-9]+$/.exec(control.value);
      return !s || Number(s[0]) > val ? null : { minVal: { minVal: val } };
    };
  }

  static maxFloatValue(val: number, errorKey: string = null) {
    return function (control: FormControl) {
      if (!control.value) return null;
      let s = /^[+-]?([0-9]*[.])?[0-9]+$/.exec(control.value);
      return !s || Number(s[0]) < val
        ? null
        : errorKey
        ? { [errorKey]: { maxVal: val } }
        : { maxVal: { maxVal: val } };
    };
  }

  static minValue(val: number) {
    return function (control: FormControl) {
      if (!control.value) return null;
      let s = /^[+-]?[0-9]+$/.exec(control.value);
      return !s || Number(s[0]) > val ? null : { minVal: { minVal: val } };
    };
  }

  static maxValue(val: number) {
    return function (control: FormControl) {
      if (!control.value) return null;
      let s = /^[+-]?[0-9]+$/.exec(control.value);
      return !s || Number(s[0]) < val ? null : { maxVal: { maxVal: val } };
    };
  }

  static emailValidator(control: FormControl) {
    if (!control.value) return null;
    const emailInput = trimString(control.value);
    if (EmailValidator.validate(emailInput)) {
      if (/^([\w-\.]+@([\w-]+\.)+[\w-]{2,20})?$/.test(control.value)) {
        return null;
      } else {
        return { email: true };
      }
    }
    return { email: true };
  }

  static emailValidatorMinLength(control: FormControl) {
    if (!control.value) return null;
    const emailInput = trimString(control.value);
    if (EmailValidator.validate(emailInput)) {
      if (/^(([\w-\.]{2,})+@([\w-]+\.)+[\w-]{2,3})?$/.test(control.value)) {
        return null;
      } else {
        return { email: true };
      }
    }
    return { email: true };
  }

  static onlyAlphabetsValidator(errorKey: string) {
    return function (control: FormControl) {
      if (!control.value) return null;
      return /^[a-zA-Z][a-zA-Z ]*$/.test(control.value)
        ? null
        : { [errorKey]: true };
    };
  }

  static differentPassword(control: FormControl) {
    if (!control.value) return null;
    return control.parent.get('old_password').value !== control.value
      ? null
      : { differentPassword: true };
  }

  static onlyAlphaNumericValidator() {
    return function (control: AbstractControl) {
      if (!control.value) return null;
      return /^[a-zA-Z0-9 ]*$/.test(control.value)
        ? null
        : { alphaNumeric: true };
    };
  }
}

const trimSpaces = (str: string) => {
  return str.replace(/\s{1,}/g, '');
};
