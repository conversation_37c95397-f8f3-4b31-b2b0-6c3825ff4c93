export const environment = {
    envt: 'uat',
    production: false,
    apiURL: 'api/v1/',

    baseURL: 'https://lrb-web-api-uat.azurewebsites.net/',
    identityURL: 'https://lrb-identity-api-uat-gtf2dfdpd9cfa2b7.centralindia-01.azurewebsites.net/',
    whatsAppBaseURL: 'https://lrb-uat-whatsapp-api.azurewebsites.net/',
    analyticsURL: 'https://lrb-prd-analytics.leadrat.com/',
    thisBaseURL: 'https://this-v3-h2ggexbrfkc7dmf2.centralindia-01.azurewebsites.net/',

    //Google
    googleOAuthEndpoint: 'https://accounts.google.com/o/oauth2/v2/auth',
    googleClientId:
        '************-ck4o1bk18icvis189juet11neb2e4q96.apps.googleusercontent.com',
    googleScope:
        'https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/gmail.readonly',

    //AWS S3
    s3ImageBucketURL: 'https://s3-ldrt-ng-hosting-uat-aps1-001.s3.ap-south-1.amazonaws.com/',
    s3BucketName: 's3-ldrt-main-uat-aps1-001',

    //Integrated Apps
    engageToURL: 'https://app.engageto.in/',

    //Firebase  Details
    firebaseConfig: {
        apiKey: "AIzaSyAX3q9mzzCJ8bl-ciWmcjsq_PrEFrdRbaE",
        authDomain: "leadrat-black-web-uat.firebaseapp.com",
        projectId: "leadrat-black-web-uat",
        storageBucket: "leadrat-black-web-uat.firebasestorage.app",
        messagingSenderId: "************",
        appId: "1:************:web:629bc2c0fbb4bd53ab8846",
        measurementId: "G-VLZGF1HYYQ",
        vapidKey: 'BMYoZ1lmdnQH-MIDTGNi3Mv0gyvmtZ-0jgSzJw4PAusengdr2eJKxcy8ue1ABmPIcSDQn0DQt8Ju7LDkIteFpmI'
    },
};
