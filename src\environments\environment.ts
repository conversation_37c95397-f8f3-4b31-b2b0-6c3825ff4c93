export const environment = {
  envt: 'qa',
  production: false,
  apiURL: 'api/v1/',

  baseURL: 'https://qa-lrb-webapi.leadrat.info/',
  identityURL: 'https://lrb-qa-identity.leadrat.info/',
  whatsAppBaseURL: 'https://lrb-qa-whatsapp.leadrat.info/',
  analyticsURL: 'https://lrb-prd-analytics.leadrat.com/',
  thisBaseURL: 'https://this-v3-h2ggexbrfkc7dmf2.centralindia-01.azurewebsites.net/',

  //Google
  googleOAuthEndpoint: 'https://accounts.google.com/o/oauth2/v2/auth',
  googleClientId:
    '************-ck4o1bk18icvis189juet11neb2e4q96.apps.googleusercontent.com',
  googleScope:
    'https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/gmail.readonly',

  //AWS S3
  s3ImageBucketURL: 'https://qleadrat-black.s3.ap-south-1.amazonaws.com/',
  s3BucketName: 'qleadrat-black',

  //Integrated Apps
  engageToURL: 'https://app.engagetod.in/',

  //Firebase  Details
  firebaseConfig: {
    apiKey: 'AIzaSyBDiowb8mIKKJFv-EWmlztIVaf3-lTlRdg',
    authDomain: 'leadrat-black-web-qa.firebaseapp.com',
    projectId: 'leadrat-black-web-qa',
    storageBucket: 'leadrat-black-web-qa.appspot.com',
    messagingSenderId: '************',
    appId: '1:************:web:269d83407355aa2dc776b1',
    measurementId: 'G-ZWHBK5EEL8',
    vapidKey: 'BNNDgcG-GMR-R6UEFFlGZfUXcBA00lw8dGoZCpXYAI0sQIRrnwgr7F_pIjCi-z9LSZQxjzW5EEhRATcOEu9oewI'
  },
};
