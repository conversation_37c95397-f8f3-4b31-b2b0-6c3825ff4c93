<ng-container *ngIf="params.data.isArchived else actions">
  <div class="d-flex">
    <div title="Restore" class="bg-blue-450 icon-badge" (click)="openArchiveModal(params.data)"><span
        class="icon ic-update m-auto ic-xxs" id="clkRestoreProperty" data-automate-id="clkRestoreProperty"></span></div>
    <div title="Delete Permanently" *ngIf="canPermanentDelete" class="bg-light-red icon-badge" id="clkDeletePermanently"
      data-automate-id="clkDeletePermanently" (click)="deletePermanently(params.data)"><span
        class="icon ic-delete m-auto ic-xxs"></span></div>
  </div>
</ng-container>
<ng-template #actions>
  <div class="d-flex mt-6">
    <div *ngIf="currentPath === '/properties/manage-listing' && canListing && params?.data?.listingStatus !== 4"
      [title]="params.data?.shouldVisisbleOnListing ? 'Delist' : 'List'"
      [ngClass]="params.data?.shouldVisisbleOnListing ? 'bg-dark-red-20' : 'bg-dark-blue-400'" class="icon-badge"
      (click)="params.data?.shouldVisisbleOnListing ? listingProperty('delist',params?.data) : listingProperty('list',params?.data)">
      <span [ngClass]="params.data?.shouldVisisbleOnListing ? 'ic-cancel-listing' : 'ic-tick-listing'"
        class="icon m-auto ic-xxs" id="clkEditProperty" data-automate-id="clkEditProperty"></span>
    </div>
    <div title="Edit" *ngIf="canEdit" class="bg-accent-green icon-badge" (click)="editProperty(params.data)">
      <span class="icon ic-pen m-auto ic-xxs" id="clkEditProperty" data-automate-id="clkEditProperty"></span>
    </div>
    <div *ngIf="canAssign" [title]="currentPath === '/properties/manage-listing' ? 'listing By' : 'Assign To'"
      class="bg-blue-800 icon-badge" id="clkAssignProject" data-automate-id="clkAssignProject"
      (click)="openAssignmentModal(assign)"><span class="icon ic-assign-to m-auto ic-xxs"></span></div>
    <share-external [data]="params.data" [key]="'share-property'" [mailCount]="params.data?.emailShareCount"
      [whatsAppCount]="params.data?.whatsAppShareCount" [moduleName]="'property'"></share-external>
    <div *ngIf="currentPath === '/properties/manage-listing' && canCloneProperty" title="Clone"
      class="bg-dark-red-40 icon-badge" (click)="cloneProperty(params.data)">
      <span class="icon ic-split-arrows ic-white m-auto ic-xxxs" id="clkEditProperty"
        data-automate-id="clkEditProperty"></span>
    </div>
    <div title="Copy Microsite URL" class="bg-brown icon-badge" (click)="copyUrl()"><span
        class="icon ic-copy-clipboard m-auto ic-xxxs"></span></div>
    <div title="Delete" *ngIf="canDelete" class="bg-light-red icon-badge" id="clkArchiveProperty"
      data-automate-id="clkArchiveProperty" (click)="openArchiveModal(params.data)"><span
        class="icon ic-delete m-auto ic-xxs"></span></div>
    <a [href]="'tel:'+params.data?.ownerDetails?.phone" (click)="initiateCall()">
      <div *ngIf="params.data?.ownerDetails?.phone && canViewOwnerInfo"
        [title]="params.data?.callShareCount > 0 ? 'Call: ' + params.data?.callShareCount : 'Call'"
        class="bg-accent-blue position-relative icon-badge">
        <span class="icon ic-Call m-auto ic-xxs"></span>
        <span *ngIf="params.data?.callShareCount"
          class="position-absolute ntop-14 text-xxs nright-14 dot dot-md bg-red mr-6">
          {{params.data?.callShareCount >= 100 ? '99+' : params.data?.callShareCount}}</span>
      </div>
    </a>
    <!-- Delete Property -->
    <!-- <a class="bg-light-red icon-badge" id="clkDeleteProperty" data-automate-id="clkDeleteProperty"
      (click)="openDeleteModal(params.data)"><span class="icon ic-pen m-auto ic-xxs"></span></a> -->
  </div>
</ng-template>
<ng-template #assign>
  <form class="h-100vh text-coal">
    <div class="bg-coal w-100 px-16 py-12 text-white flex-between">
      <h3 class="fw-semi-bold">User {{ 'LEADS.assignment' | translate }}</h3>
      <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
    </div>
    <div class="p-16">
      <div class="bg-light-pearl mt-20 br-6 flex-between">
        <div class="flex-column pt-10 pl-10 pb-20">
          <div class="fw-semi-bold fv-sm-caps"> Property
            {{'GLOBAL.name' | translate}}</div>
          <div class="fw-700 text-large">{{params.data?.title}}</div>
        </div>
        <div><img src="../../../../assets/images/profile.svg" alt="" class="mt-8" /></div>
      </div>
      <div class="align-center" *ngIf="currentPath === '/properties/manage-listing'">
        <div *ngFor="let listingTypeOption of listingTypeOptions" class="form-check form-check-inline">
          <div class="align-center mt-8">
            <input type="radio" [id]="listingTypeOption?.value + 'listingTypeOptionRadio'"
              class="radio-check-input border-remove" [value]="listingTypeOption.value"
              [formControl]="isListingOnBehalf" name="isListingOnBehalf">
            <label class="fw-600 text-coal cursor-pointer text-sm ml-8"
              [for]="listingTypeOption?.value + 'listingTypeOptionRadio'"> {{ listingTypeOption.label }} </label>
          </div>
        </div>
      </div>
      <div class="field-label" *ngIf="isListingOnBehalf.value !== true"> {{'SETTINGS.select-user' | translate}}
      </div>
      <div class="ng-select-sm-gray" *ngIf="isListingOnBehalf.value !== true">
        <ng-select [virtualScroll]="true" placeholder="ex. Mounika Pampana"
          [items]="canAssignToAny ? allUserList : userList" [multiple]="true" [closeOnSelect]="false"
          bindLabel="fullName" bindValue="id" name="assignedUser" [formControl]="assignedUser" class="bg-white"
          (change)="onUserSelect($event)">
          <ng-template ng-label-tmp let-item="item" let-clear="clear">
            <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="onClear(item)"></span>
            <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
          </ng-template>
          <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
            <div class="checkbox-container">
              <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}" [checked]="item$.selected"
                [disabled]="!item.isActive">
              <span class="checkmark"></span>
              <span class="text-truncate-1 break-all">{{item.firstName}} {{item.lastName}}</span>
              <span class="error-message-custom top-13" *ngIf="!item.isActive">( Disabled )</span>
            </div>
          </ng-template>
        </ng-select>
      </div>
      <div class="ng-select-sm-gray"
        *ngIf="currentPath === '/properties/manage-listing' && isListingOnBehalf.value === true">
        <div class="field-label">{{'SETTINGS.select-user' | translate}}</div>
        <ng-select [virtualScroll]="true" placeholder="Select User" name="listingOnBehalfUser"
          [formControl]="listingOnBehalfUser" ResizableDropdown class="bg-white" (change)="onUserSelect($event)">

          <ng-container *ngIf="!canAssignToAny">
            <ng-option *ngFor="let user of activeUsers" [value]="user.id">
              {{ user.firstName }} {{ user.lastName }}
              <span class="d-none">{{ user.fullName }}</span>
            </ng-option>

            <ng-option *ngFor="let user of deactiveUsers" [value]="user.id" [disabled]="true">
              <div class="dropdown-position">
                <span class="text-truncate-1 break-all">{{ user.firstName }} {{ user.lastName }}</span>
                <span class="d-none">{{ user.fullName }}</span>
                <span class="text-disabled" *ngIf="!user.isActive">(Disabled)</span>
              </div>
            </ng-option>
          </ng-container>

          <ng-container *ngIf="canAssignToAny">
            <ng-option *ngFor="let user of allActiveUsers" [value]="user.id">
              {{ user.firstName }} {{ user.lastName }}
              <span class="d-none">{{ user.fullName }}</span>
            </ng-option>

            <ng-option *ngFor="let user of allDeActiveUsers" [value]="user.id" [disabled]="true">
              <div class="dropdown-position">
                <span class="text-truncate-1 break-all">{{ user.firstName }} {{ user.lastName }}</span>
                <span class="d-none">{{ user.fullName }}</span>
                <span class="text-disabled" *ngIf="!user.isActive">(Disabled)</span>
              </div>
            </ng-option>
          </ng-container>

        </ng-select>
      </div>

      <div class="flex-end mt-20">
        <button class="btn-gray mr-20" (click)="modalService.hide()">
          {{ 'BUTTONS.cancel' | translate }}</button>
        <button class="btn-coal" (click)="assignAccount()">
          {{ 'BUTTONS.save' | translate }}</button>
      </div>
      <div class="mt-10" *ngIf="assignedUserDetails?.length">
        <div class="d-flex">
          <div class="field-label-underline">{{'GLOBAL.assigned-to'| translate}}
          </div>
        </div>
        <div class="scrollbar h-100-354 mt-12">
          <div class="flex-between mb-12" *ngFor="let user of assignedUserDetails">
            <div class="align-center">
              <div class="dot dot-xl bg-pearl-90 mr-2">
                <span class="fw-semi-bold text-normal text-white text-uppercase">
                  {{user ?
                  getAssignedToDetails(user, canAssignToAny ? allAssignUserList :userList)?.firstName[0] +
                  getAssignedToDetails(user, canAssignToAny ? allAssignUserList :userList)?.lastName[0] :
                  '--'}}
                </span>
              </div>
              <div class="align-center">
                <div class="fw-semi-bold text-large text-coal text-truncate-1 break-all"
                  [title]="getAssignedToDetails(user, canAssignToAny ? allAssignUserList : userList, true) || ''">
                  {{getAssignedToDetails(user, canAssignToAny ?
                  allAssignUserList : userList, true) || '--'}}
                </div>
                <span *ngIf="!getAssignedToDetails(user, canAssignToAny ? allAssignUserList : userList)?.isActive"
                  class="text-danger text-xxs">(Disable)</span>
              </div>
            </div>
            <div title="Delete" class="bg-light-red icon-badge" id="clkPermanentDeleteLead"
              data-automate-id="clkPermanentDeleteLead" (click)="deleteAssignUser(user)">
              <span class="icon ic-delete m-auto ic-xxs"></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>
</ng-template>