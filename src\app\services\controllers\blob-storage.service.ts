import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, Subject, takeUntil } from 'rxjs';

import { FolderNamesS3 } from 'src/app/app.enum';
import { BaseService } from 'src/app/services/shared/base.service';
import { environment as env } from 'src/environments/environment';

@Injectable({
    providedIn: 'root',
})
export class BlobStorageService extends BaseService<any> {
    private cancelRequest = new Subject<void>();
    serviceBaseUrl: string = '';
    bucketName: string = env.s3BucketName;

    constructor(private http: HttpClient) {
        super(http);
        this.serviceBaseUrl = `${env.baseURL}${this.getResourceUrl()}`;
    }

    getResourceUrl(): string {
        return 'api/blobstorage/';
    }

    uploadPropertyGallery(imageFiles: Array<string> = []) {
        return this.http.post(
            `${this.serviceBaseUrl}image/base64/${this.bucketName}/propertyGallery`,
            imageFiles
        );
    }

    uploadBookingForm(imageFiles: Array<string> = []): Observable<any> {
        return this.http
            .post(
                `${this.serviceBaseUrl}image/base64/${this.bucketName}/bookingForm`,
                imageFiles
            )
            .pipe(takeUntil(this.cancelRequest));
    }

    uploadDocBase64(
        payload: Array<string>,
        folderName: FolderNamesS3
    ): Observable<any> {
        return this.http.post(
            `${this.serviceBaseUrl}doc/base64/${this.bucketName}/${folderName}`,
            payload
        );
    }

    uploadImageBase64(
        payload: Array<string>,
        folderName: FolderNamesS3
    ): Observable<any> {
        return this.http.post(
            `${this.serviceBaseUrl}image/base64/${this.bucketName}/${folderName}`,
            payload
        );
    }

    uploadDoc(file: any, folderName: FolderNamesS3): Observable<any> {
        let formData = new FormData();
        formData.append('Files', file);
        formData.append('BucketName', this.bucketName);
        formData.append('FolderName', folderName);
        return this.http.post(`${this.serviceBaseUrl}doc/formfile`, formData);
    }

    uploadDocFormFile(files: Array<string> = [], folderName: FolderNamesS3) {
        let payload: any = {
            files: files,
        };
        return this.http.post(`${this.serviceBaseUrl}doc/formfile/${this.bucketName}/${folderName}`, payload);
    }

    cancelUpload() {
        this.cancelRequest.next();
        this.cancelRequest = new Subject<void>();
    }
}
