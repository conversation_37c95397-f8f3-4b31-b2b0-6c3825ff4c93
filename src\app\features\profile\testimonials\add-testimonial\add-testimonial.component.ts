import { Component, EventEmitter, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { FolderNamesS3 } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { Testimonial } from 'src/app/core/interfaces/profile.interface';
import { validateAllFormFields } from 'src/app/core/utils/common.util';
import {
  AddTestimonial,
  UpdateTestimonial,
} from 'src/app/reducers/profile/profile.actions';
import { BlobStorageService } from 'src/app/services/controllers/blob-storage.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'profile-add-testimonial',
  templateUrl: './add-testimonial.component.html',
})
export class AddTestimonialComponent implements OnI<PERSON>t, On<PERSON><PERSON>roy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  testimonialForm: FormGroup;
  testimonial: Testimonial = {} as Testimonial;
  s3BucketUrl: string = environment.s3ImageBucketURL;
  files: any[] = [];
  testimonialPic: any;
  constructor(
    private fb: FormBuilder,
    private modalService: BsModalService,
    private store: Store<AppState>,
    private s3UploadService: BlobStorageService
  ) {
    this.testimonialForm = this.fb.group({
      imageUrl: [''],
      name: [null, Validators.required],
      company: [''],
      note: [''],
    });
  }

  ngOnInit() {
    if (this.testimonial) {
      this.testimonialForm.patchValue({
        name: this?.testimonial?.givenBy,
        company: this?.testimonial?.companyName,
        note: this?.testimonial?.description,
      });
    }
  }

  onFileSelected(event: any) {
    if (event.target.files && event.target.files[0]) {
      for (let i = 0; i < event.target.files.length; i++) {
        this.files.push(event.target.files[i]);
        let reader = new FileReader();
        reader.onload = (eventOnload: any) => {
          const image = new Image();
          image.src = eventOnload.target.result;
          image.onload = (rs: any) => {
            const img_height = rs.currentTarget['height'];
            const img_width = rs.currentTarget['width'];
          };
          this.testimonialPic = eventOnload.target.result;
          this.postData();
        };
        reader.readAsDataURL(event.target.files[i]);
      }
    }
  }

  deleteProfilePic() {
    this.testimonialPic = '';
    this.testimonial = {
      ...this.testimonial,
      imageURL: '',
    };
    this.testimonialForm.controls['imageUrl'].reset();
  }

  postData() {
    if (this.testimonialPic?.includes('data:')) {
      this.s3UploadService
        .uploadImageBase64([this.testimonialPic], FolderNamesS3.Images)
        .pipe(takeUntil(this.stopper))
        .subscribe((response: any) => {
          if (response.data.length) {
            this.testimonialPic = response.data?.[0];
          }
        });
    }
  }

  onSave() {
    let testimonial = this.testimonialForm.value;
    if (this.testimonialForm.invalid) {
      validateAllFormFields(this.testimonialForm);
      return;
    }
    if (this.testimonial.id) {
      let payload: Testimonial = {
        id: this.testimonial.id,
        givenBy: testimonial.name,
        companyName: testimonial.company,
        description: testimonial.note,
        imageURL: this.testimonialPic || this.testimonial.imageURL || '',
      };
      this.store.dispatch(new UpdateTestimonial(payload));
    } else {
      let payload: Testimonial = {
        givenBy: testimonial.name,
        companyName: testimonial.company,
        description: testimonial.note,
        imageURL: this.testimonialPic || '',
      };
      this.store.dispatch(new AddTestimonial(payload));
    }
    this.testimonialPic = null;
    this.modalService.hide();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
