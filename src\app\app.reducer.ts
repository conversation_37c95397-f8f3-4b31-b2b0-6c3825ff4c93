import {
  dashboardReducer,
  DashboardState,
} from 'src/app/reducers/dashboard/dashboard.reducers';
import {
  forgotPasswordReducer,
  ForgotPasswordState,
} from 'src/app/reducers/forgot-password/forgot-password.reducers';
import {
  integrationReducer,
  IntegrationState,
} from 'src/app/reducers/Integration/integration.reducer';
import { leadReducer, LeadState } from 'src/app/reducers/lead/lead.reducer';
import {
  loaderReducer,
  LoaderState,
} from 'src/app/reducers/loader/loader.reducer';
import { loginReducer, LoginState } from 'src/app/reducers/login/login.reducer';
import {
  masterDataReducer,
  MasterDataState,
} from 'src/app/reducers/master-data/master-data.reducer';
import {
  placesReducer,
  PlacesState,
} from 'src/app/reducers/places/places.reducer';
import {
  profileReducer,
  ProfileState,
} from 'src/app/reducers/profile/profile.reducers';
import {
  propertyReducer,
  PropertyState,
} from 'src/app/reducers/property/property.reducer';
import { teamsReducer, TeamsState } from 'src/app/reducers/teams/teams.reducer';
import { todoReducer, TodoState } from 'src/app/reducers/todo/todo.reducer';
import { amenitiesAttribuesReducer, AmenitiesAttributesState } from './reducers/amenities-attributes/amenities-attributes.reducer';
import {
  AnalyticsReducer,
  AnalyticsState,
} from './reducers/analytics/analytics.reducer';
import {
  attendanceReducer,
  AttendanceState,
} from './reducers/attendance/attendance.reducer';
import {
  automationReducer,
  AutomationState,
} from './reducers/automation/automation.reducer';
import {
  customSubStatusReducer,
  CustomSubStatusState,
} from './reducers/custom-sub-status/custom-sub-status.reducers';
import {
  customTagsReducer,
  CustomTagState,
} from './reducers/custom-tags/custom-tags.reducer';
import {
  dataReportsReducer,
  DataReportsState,
} from './reducers/data-reports/data-reports.reducers';
import {
  dataManagementReducer,
  DataManagementState,
} from './reducers/data/data-management.reducer';
import {
  emailSMTPReducer,
  EmailSMTPState,
} from './reducers/email/email-settings.reducer';
import { FieldsReducer, FieldsState } from './reducers/fields/fields.reducer';
import { filterReducer, FilterState } from './reducers/filter/filter.reducer';
import {
  globalSettingsReducer,
  GlobalSettingsState,
} from './reducers/global-settings/global-settings.reducer';
import {
  invoiceReducer,
  InvoiceState,
} from './reducers/invoice/invoice.reducer';
import {
  listingSiteReducer,
  ListingSiteState,
} from './reducers/listing-site/listing-site.reducer';
import {
  marketingReducer,
  MarketingState,
} from './reducers/manage-marketing/marketing.reducer';
import { notificationInfoReducer, NotificationInfoState } from './reducers/notification-info/notification-info.reducer';
import {
  permissionsReducer,
  PermissionsState,
} from './reducers/permissions/permissions.reducers';
import {
  projectReducer,
  ProjectState,
} from './reducers/project/project.reducer';
import { qrFormReducer, QrFormState } from './reducers/qr-form/qr-form.reducer';
import { referenceIdReducer, ReferenceIdState } from './reducers/reference-id-management/reference-id-management.reducer';
import {
  reportsReducer,
  ReportsState,
} from './reducers/reports/reports.reducer';
import {
  shiftTimingReducer,
  ShiftTimingState,
} from './reducers/shift-timing/shift-timing.reducers';
import { siteReducer, SiteState } from './reducers/site/site.reducer';
import {
  customStatusReducer,
  CustomStatusState,
} from './reducers/status/status.reducer';
import {
  templateReducer,
  TemplateState,
} from './reducers/template/template.reducer';
import {
  whatsappCloudReducer,
  WhatsappCloudState,
} from './reducers/whatsapp-cloud/whatsapp-cloud.reducer';
import {
  whatsappReducer,
  WhatsappState,
} from './reducers/whatsapp/whatsapp.reducer';
import { tenantReducer, TenantState } from './reducers/tenant/tenant.reducer';
import { productsReducer, ProductsState } from './reducers/this/products/products.reducer';

export interface AppState {
  fields: FieldsState;
  loader: LoaderState;
  masterData: MasterDataState;
  integrationData: IntegrationState;
  lead: LeadState;
  places: PlacesState;
  todo: TodoState;
  property: PropertyState;
  teams: TeamsState;
  auth: LoginState;
  profile: ProfileState;
  dashboard: DashboardState;
  forgetPassword: ForgotPasswordState;
  globalSettings: GlobalSettingsState;
  permissions: PermissionsState;
  template: TemplateState;
  whatsapp: WhatsappState;
  whatsappCloud: WhatsappCloudState;
  reports: ReportsState;
  project: ProjectState;
  attendance: AttendanceState;
  automation: AutomationState;
  site: SiteState;
  dataManagement: DataManagementState;
  qrForm: QrFormState;
  dataReports: DataReportsState;
  customSubStatus: CustomSubStatusState;
  invoice: InvoiceState;
  customStatus: CustomStatusState;
  customTags: CustomTagState;
  login: LoginState;
  emailSettings: EmailSMTPState;
  shiftTiming: ShiftTimingState;
  amenitiesAttributes: AmenitiesAttributesState;
  marketing: MarketingState;
  listingSite: ListingSiteState;
  filter: FilterState;
  referenceIdManagement: ReferenceIdState;
  notificationInfo: NotificationInfoState;
  analytics: AnalyticsState;
  tenant: TenantState;
  products: ProductsState;
}
export const reducer = {
  loader: loaderReducer,
  masterData: masterDataReducer,
  integrationData: integrationReducer,
  lead: leadReducer,
  places: placesReducer,
  todo: todoReducer,
  property: propertyReducer,
  teams: teamsReducer,
  auth: loginReducer,
  profile: profileReducer,
  dashboard: dashboardReducer,
  forgetPassword: forgotPasswordReducer,
  globalSettings: globalSettingsReducer,
  permissions: permissionsReducer,
  template: templateReducer,
  whatsapp: whatsappReducer,
  whatsappCloud: whatsappCloudReducer,
  reports: reportsReducer,
  project: projectReducer,
  attendance: attendanceReducer,
  automation: automationReducer,
  site: siteReducer,
  dataManagement: dataManagementReducer,
  qrForm: qrFormReducer,
  dataReports: dataReportsReducer,
  customSubStatus: customSubStatusReducer,
  invoice: invoiceReducer,
  customStatus: customStatusReducer,
  customTags: customTagsReducer,
  login: loginReducer,
  emailSettings: emailSMTPReducer,
  shiftTiming: shiftTimingReducer,
  amenitiesAttributes: amenitiesAttribuesReducer,
  fields: FieldsReducer,
  marketing: marketingReducer,
  listingSite: listingSiteReducer,
  filter: filterReducer,
  analytics: AnalyticsReducer,
  referenceIdManagement: referenceIdReducer,
  notificationInfo: notificationInfoReducer,
  tenant: tenantReducer,
  products: productsReducer,
};
